{"name": "AI-Powered Lead Scoring & Routing", "nodes": [{"parameters": {"path": "lead-scoring", "options": {}}, "id": "webhook-trigger", "name": "New Lead Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "https://api.clearbit.com/v2/enrichment/find", "authentication": "predefinedCredentialType", "nodeCredentialType": "clearbitApi", "options": {"queryParameters": {"parameters": [{"name": "email", "value": "={{ $json.email }}"}]}}}, "id": "enrich-lead", "name": "Enrich Lead Data (Clearbit)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Analyze this lead data and provide a comprehensive lead score from 0-100:\n\nLead Information:\n- Name: {{ $json.name }}\n- Email: {{ $json.email }}\n- Company: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || 'Unknown' }}\n- Industry: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.category?.industry || 'Unknown' }}\n- Company Size: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.employees || 'Unknown' }}\n- Revenue: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.annualRevenue || 'Unknown' }}\n- Job Title: {{ $('Enrich Lead Data (Clearbit)').item.json.person?.employment?.title || $json.job_title || 'Unknown' }}\n- Seniority: {{ $('Enrich Lead Data (Clearbit)').item.json.person?.employment?.seniority || 'Unknown' }}\n- Location: {{ $('Enrich Lead Data (Clearbit)').item.json.person?.location?.city || 'Unknown' }}\n- Source: {{ $json.source || 'Unknown' }}\n- Interest: {{ $json.interest || 'Unknown' }}\n\nScoring Criteria:\n1. Company Size (0-25 points): Larger companies = higher score\n2. Job Title/Seniority (0-25 points): Decision makers = higher score\n3. Industry Fit (0-20 points): Target industries = higher score\n4. Lead Source (0-15 points): High-intent sources = higher score\n5. Engagement Level (0-15 points): Specific interest = higher score\n\nReturn ONLY a JSON object with this structure:\n{\n  \"lead_score\": 85,\n  \"priority\": \"High\",\n  \"reasoning\": \"Brief explanation of score\",\n  \"recommended_action\": \"Immediate follow-up recommended\",\n  \"assigned_rep\": \"enterprise\" or \"smb\" or \"inbound\"\n}"}]}, "options": {"temperature": 0.3, "maxTokens": 300}}, "id": "ai-lead-scoring", "name": "AI Lead Scoring Analysis", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "largerEqual"}, "conditions": [{"leftValue": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}", "rightValue": 80, "operation": "largerEqual"}], "combineOperation": "any"}}, "id": "check-score-high", "name": "High Score (80+)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "largerEqual"}, "conditions": [{"leftValue": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}", "rightValue": 50, "operation": "largerEqual"}], "combineOperation": "any"}}, "id": "check-score-medium", "name": "Medium Score (50-79)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"resource": "contact", "operation": "create", "additionalFields": {"company": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || $json.company }}", "jobTitle": "={{ $('Enrich Lead Data (Clearbit)').item.json.person?.employment?.title || $json.job_title }}", "phone": "={{ $json.phone }}", "website": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.domain }}", "lifecycleStage": "lead", "leadStatus": "new", "customProperties": {"lead_score": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}", "lead_priority": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).priority }}", "lead_source": "={{ $json.source }}", "ai_reasoning": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).reasoning }}", "company_size": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.employees }}", "annual_revenue": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.annualRevenue }}", "industry": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.category?.industry }}"}}, "email": "={{ $json.email }}", "firstName": "={{ $json.first_name || $json.name?.split(' ')[0] }}", "lastName": "={{ $json.last_name || $json.name?.split(' ').slice(1).join(' ') }}"}, "id": "create-hubspot-contact", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.sales_team_chat || '@sales_team' }}", "text": "🔥 HIGH PRIORITY LEAD ALERT!\n\n👤 Name: {{ $json.name }}\n📧 Email: {{ $json.email }}\n🏢 Company: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || $json.company || 'Unknown' }}\n💼 Title: {{ $('Enrich Lead Data (Clearbit)').item.json.person?.employment?.title || $json.job_title || 'Unknown' }}\n📊 Lead Score: {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}/100\n🎯 Priority: {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).priority }}\n💡 AI Analysis: {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).reasoning }}\n🎬 Action: {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).recommended_action }}\n\n⚡ IMMEDIATE FOLLOW-UP REQUIRED!", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-high-priority", "name": "Notify High Priority Lead", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1120, 100]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.sales_team_chat || '@sales_team' }}", "text": "📈 Medium Priority Lead\n\n👤 {{ $json.name }} ({{ $json.email }})\n🏢 {{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || $json.company || 'Unknown' }}\n📊 Score: {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}/100\n💡 {{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).reasoning }}\n\n📅 Follow up within 24-48 hours", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-medium-priority", "name": "Notify Medium Priority Lead", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"operation": "append", "documentId": "{{ $json.leads_sheet_id || '1BvEQzKj9Kn8YxVqGpF2mH3sL4tR5uI6oP7qW8eR9tY0' }}", "sheetName": "Low Priority Leads", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ new Date().toISOString() }}", "name": "={{ $json.name }}", "email": "={{ $json.email }}", "company": "={{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || $json.company }}", "job_title": "={{ $('Enrich Lead Data (Clearbit)').item.json.person?.employment?.title || $json.job_title }}", "lead_score": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).lead_score }}", "priority": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).priority }}", "reasoning": "={{ JSON.parse($('AI Lead Scoring Analysis').item.json.response).reasoning }}", "source": "={{ $json.source }}", "status": "Nurture Campaign"}}}, "id": "add-to-nurture", "name": "Add to Nurture Campaign", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1120, 600]}, {"parameters": {"jsCode": "// Determine sales rep assignment based on lead data\nconst leadData = JSON.parse($('AI Lead Scoring Analysis').item.json.response);\nconst companySize = $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.employees || 0;\nconst revenue = $('Enrich Lead Data (Clearbit)').item.json.company?.metrics?.annualRevenue || 0;\n\nlet assignedRep = 'inbound'; // default\n\n// Enterprise assignment (large companies)\nif (companySize > 500 || revenue > 10000000) {\n  assignedRep = 'enterprise';\n}\n// SMB assignment (medium companies)\nelse if (companySize > 50 || revenue > 1000000) {\n  assignedRep = 'smb';\n}\n// Inbound assignment (small companies or unknown)\nelse {\n  assignedRep = 'inbound';\n}\n\nreturn {\n  ...items[0].json,\n  assigned_rep: assignedRep,\n  lead_score: leadData.lead_score,\n  priority: leadData.priority,\n  reasoning: leadData.reasoning\n};"}, "id": "determine-assignment", "name": "Determine Rep Assignment", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"resource": "task", "operation": "create", "additionalFields": {"subject": "Follow up with high-priority lead: {{ $json.name }}", "description": "Lead Score: {{ $json.lead_score }}/100\\nCompany: {{ $('Enrich Lead Data (Clearbit)').item.json.company?.name || $json.company }}\\nReasoning: {{ $json.reasoning }}\\n\\nImmediate follow-up required for this high-priority lead.", "priority": "High", "status": "Not Started", "dueDate": "={{ new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() }}"}, "contactId": "={{ $('Create HubSpot Contact').item.json.id }}"}, "id": "create-follow-up-task", "name": "Create Follow-up Task", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1560, 200]}], "connections": {"New Lead Webhook": {"main": [[{"node": "Enrich Lead Data (Clearbit)", "type": "main", "index": 0}]]}, "Enrich Lead Data (Clearbit)": {"main": [[{"node": "AI Lead Scoring Analysis", "type": "main", "index": 0}]]}, "AI Lead Scoring Analysis": {"main": [[{"node": "High Score (80+)", "type": "main", "index": 0}, {"node": "Medium Score (50-79)", "type": "main", "index": 0}, {"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "High Score (80+)": {"main": [[{"node": "Notify High Priority Lead", "type": "main", "index": 0}]]}, "Medium Score (50-79)": {"main": [[{"node": "Notify Medium Priority Lead", "type": "main", "index": 0}], [{"node": "Add to Nurture Campaign", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Determine Rep Assignment", "type": "main", "index": 0}]]}, "Determine Rep Assignment": {"main": [[{"node": "Create Follow-up Task", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "sales", "name": "Sales"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "lead-scoring", "name": "Lead Scoring"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-automation", "name": "AI Automation"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "1"}