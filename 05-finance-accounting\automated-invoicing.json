{"name": "Automated Invoicing & Payment Tracking System", "nodes": [{"parameters": {"cronExpression": "0 9 1 * *", "triggerAtStartup": false}, "id": "monthly-trigger", "name": "Monthly Billing Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"operation": "read", "documentId": "{{ $json.customers_sheet_id || '1CustomerData123' }}", "sheetName": "Active Customers", "options": {"range": "A:Z"}}, "id": "read-customers", "name": "Read Customer Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "equal"}, "conditions": [{"leftValue": "={{ $json.billing_status }}", "rightValue": "active", "operation": "equal"}, {"leftValue": "={{ new Date($json.next_billing_date) <= new Date() }}", "rightValue": true, "operation": "equal"}], "combineOperation": "all"}}, "id": "filter-billable", "name": "Filter Billable Customers", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"jsCode": "// Calculate invoice details\nconst customer = items[0].json;\nconst today = new Date();\nconst invoiceNumber = `INV-${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}-${customer.customer_id}`;\n\n// Calculate line items based on subscription\nconst lineItems = [];\nconst baseAmount = parseFloat(customer.monthly_amount || 0);\nconst taxRate = parseFloat(customer.tax_rate || 0.1);\nconst discount = parseFloat(customer.discount_percent || 0);\n\n// Base subscription\nlineItems.push({\n  description: customer.plan_name || 'Monthly Subscription',\n  quantity: 1,\n  rate: baseAmount,\n  amount: baseAmount\n});\n\n// Add-ons if any\nif (customer.addons) {\n  const addons = JSON.parse(customer.addons || '[]');\n  addons.forEach(addon => {\n    lineItems.push({\n      description: addon.name,\n      quantity: addon.quantity || 1,\n      rate: addon.price,\n      amount: addon.quantity * addon.price\n    });\n  });\n}\n\nconst subtotal = lineItems.reduce((sum, item) => sum + item.amount, 0);\nconst discountAmount = subtotal * (discount / 100);\nconst taxableAmount = subtotal - discountAmount;\nconst taxAmount = taxableAmount * taxRate;\nconst total = taxableAmount + taxAmount;\n\n// Calculate due date (30 days from invoice date)\nconst dueDate = new Date(today);\ndueDate.setDate(dueDate.getDate() + 30);\n\n// Calculate next billing date\nconst nextBilling = new Date(customer.next_billing_date);\nnextBilling.setMonth(nextBilling.getMonth() + 1);\n\nreturn {\n  ...customer,\n  invoice_number: invoiceNumber,\n  invoice_date: today.toISOString().split('T')[0],\n  due_date: dueDate.toISOString().split('T')[0],\n  line_items: lineItems,\n  subtotal: subtotal,\n  discount_amount: discountAmount,\n  tax_amount: taxAmount,\n  total_amount: total,\n  next_billing_date: nextBilling.toISOString().split('T')[0]\n};"}, "id": "calculate-invoice", "name": "Calculate Invoice Details", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"resource": "invoice", "operation": "create", "additionalFields": {"invoiceNumber": "={{ $json.invoice_number }}", "dueDate": "={{ $json.due_date }}", "currency": "{{ $json.currency || 'USD' }}", "notes": "Thank you for your business! Payment is due within 30 days.", "terms": "Net 30", "lineItems": "={{ $json.line_items.map(item => ({ description: item.description, quantity: item.quantity, rate: item.rate })) }}", "discountPercent": "={{ $json.discount_percent || 0 }}", "taxRate": "={{ ($json.tax_rate || 0.1) * 100 }}", "customFields": {"subscription_plan": "{{ $json.plan_name }}", "billing_period": "{{ $json.invoice_date }} to {{ $json.next_billing_date }}", "customer_id": "{{ $json.customer_id }}"}}, "clientId": "={{ $json.quickbooks_customer_id }}"}, "id": "create-quickbooks-invoice", "name": "Create QuickBooks Invoice", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"sendTo": "={{ $json.email }}", "subject": "Invoice {{ $json.invoice_number }} - {{ $json.company_name }}", "message": "Dear {{ $json.contact_name }},\\n\\nPlease find attached your invoice for {{ $json.plan_name }}.\\n\\nInvoice Details:\\n- Invoice Number: {{ $json.invoice_number }}\\n- Amount Due: ${{ $json.total_amount.toFixed(2) }}\\n- Due Date: {{ $json.due_date }}\\n\\nYou can pay online at: {{ $json.payment_link || 'https://yourcompany.com/pay' }}\\n\\nThank you for your business!\\n\\nBest regards,\\nAccounting Team", "options": {"attachments": "invoice_{{ $json.invoice_number }}.pdf", "ccList": "{{ $json.accounting_email || '<EMAIL>' }}", "replyTo": "{{ $json.billing_email || '<EMAIL>' }}"}}, "id": "send-invoice-email", "name": "Send Invoice Email", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"operation": "update", "documentId": "={{ $('Read Customer Data').item.json.customers_sheet_id }}", "sheetName": "Active Customers", "columnToMatchOn": "customer_id", "valueToMatchOn": "={{ $json.customer_id }}", "columns": {"mappingMode": "defineBelow", "value": {"last_invoice_date": "={{ $json.invoice_date }}", "last_invoice_number": "={{ $json.invoice_number }}", "last_invoice_amount": "={{ $json.total_amount }}", "next_billing_date": "={{ $json.next_billing_date }}", "invoice_status": "sent", "quickbooks_invoice_id": "={{ $('Create QuickBooks Invoice').item.json.id }}"}}}, "id": "update-customer-record", "name": "Update Customer Record", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1560, 200]}, {"parameters": {"operation": "append", "documentId": "{{ $json.invoices_sheet_id || '1InvoiceTracking123' }}", "sheetName": "Invoices", "columns": {"mappingMode": "defineBelow", "value": {"invoice_date": "={{ $json.invoice_date }}", "invoice_number": "={{ $json.invoice_number }}", "customer_id": "={{ $json.customer_id }}", "customer_name": "={{ $json.company_name }}", "customer_email": "={{ $json.email }}", "plan_name": "={{ $json.plan_name }}", "subtotal": "={{ $json.subtotal }}", "discount_amount": "={{ $json.discount_amount }}", "tax_amount": "={{ $json.tax_amount }}", "total_amount": "={{ $json.total_amount }}", "due_date": "={{ $json.due_date }}", "status": "sent", "quickbooks_id": "={{ $('Create QuickBooks Invoice').item.json.id }}", "payment_status": "pending"}}}, "id": "log-invoice", "name": "Log Invoice to Tracking Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1780, 200]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.finance_team_chat || '@finance_team' }}", "text": "💰 Invoice Generated & Sent\n\n📄 Invoice: {{ $json.invoice_number }}\n🏢 Customer: {{ $json.company_name }}\n💵 Amount: ${{ $json.total_amount.toFixed(2) }}\n📅 Due Date: {{ $json.due_date }}\n📧 Sent to: {{ $json.email }}\n\n📊 Monthly Summary:\n- Invoices sent today: {{ $workflow.executions.length }}\n- Total billed: ${{ $workflow.executions.reduce((sum, exec) => sum + exec.total_amount, 0).toFixed(2) }}", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-finance-team", "name": "Notify Finance Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1780, 400]}, {"parameters": {"path": "payment-webhook", "options": {}}, "id": "payment-webhook", "name": "Payment Received Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "update", "documentId": "{{ $json.invoices_sheet_id || '1InvoiceTracking123' }}", "sheetName": "Invoices", "columnToMatchOn": "invoice_number", "valueToMatchOn": "={{ $json.invoice_number }}", "columns": {"mappingMode": "defineBelow", "value": {"payment_date": "={{ new Date().toISOString().split('T')[0] }}", "payment_amount": "={{ $json.amount }}", "payment_method": "={{ $json.payment_method }}", "payment_status": "paid", "transaction_id": "={{ $json.transaction_id }}"}}}, "id": "update-payment-status", "name": "Update Payment Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 600]}, {"parameters": {"resource": "payment", "operation": "create", "additionalFields": {"amount": "={{ $json.amount }}", "paymentMethod": "{{ $json.payment_method }}", "reference": "{{ $json.transaction_id }}", "notes": "Payment received via {{ $json.payment_method }} for invoice {{ $json.invoice_number }}"}, "invoiceId": "={{ $json.quickbooks_invoice_id }}"}, "id": "record-payment-quickbooks", "name": "Record Payment in QuickBooks", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"sendTo": "={{ $json.customer_email }}", "subject": "Payment Confirmation - Invoice {{ $json.invoice_number }}", "message": "Dear {{ $json.customer_name }},\\n\\nThank you for your payment!\\n\\nPayment Details:\\n- Invoice Number: {{ $json.invoice_number }}\\n- Amount Paid: ${{ $json.amount }}\\n- Payment Method: {{ $json.payment_method }}\\n- Transaction ID: {{ $json.transaction_id }}\\n- Date: {{ new Date().toLocaleDateString() }}\\n\\nYour account is now up to date.\\n\\nBest regards,\\nAccounting Team", "options": {"replyTo": "{{ $json.billing_email || '<EMAIL>' }}"}}, "id": "send-payment-confirmation", "name": "Send Payment Confirmation", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [900, 600]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.finance_team_chat || '@finance_team' }}", "text": "✅ Payment Received!\n\n💰 Amount: ${{ $json.amount }}\n📄 Invoice: {{ $json.invoice_number }}\n🏢 Customer: {{ $json.customer_name }}\n💳 Method: {{ $json.payment_method }}\n🔗 Transaction: {{ $json.transaction_id }}\n📅 Date: {{ new Date().toLocaleDateString() }}\n\n✉️ Confirmation email sent to customer", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-payment-received", "name": "Notify Payment Received", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1120, 600]}], "connections": {"Monthly Billing Trigger": {"main": [[{"node": "Read Customer Data", "type": "main", "index": 0}]]}, "Read Customer Data": {"main": [[{"node": "Filter Billable Customers", "type": "main", "index": 0}]]}, "Filter Billable Customers": {"main": [[{"node": "Calculate Invoice Details", "type": "main", "index": 0}]]}, "Calculate Invoice Details": {"main": [[{"node": "Create QuickBooks Invoice", "type": "main", "index": 0}]]}, "Create QuickBooks Invoice": {"main": [[{"node": "Send Invoice Email", "type": "main", "index": 0}]]}, "Send Invoice Email": {"main": [[{"node": "Update Customer Record", "type": "main", "index": 0}]]}, "Update Customer Record": {"main": [[{"node": "Log Invoice to Tracking Sheet", "type": "main", "index": 0}, {"node": "Notify Finance Team", "type": "main", "index": 0}]]}, "Payment Received Webhook": {"main": [[{"node": "Update Payment Status", "type": "main", "index": 0}]]}, "Update Payment Status": {"main": [[{"node": "Record Payment in QuickBooks", "type": "main", "index": 0}]]}, "Record Payment in QuickBooks": {"main": [[{"node": "Send Payment Confirmation", "type": "main", "index": 0}]]}, "Send Payment Confirmation": {"main": [[{"node": "Notify Payment Received", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "finance", "name": "Finance"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "invoicing", "name": "Invoicing"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "payment-tracking", "name": "Payment Tracking"}], "triggerCount": 2, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "1"}