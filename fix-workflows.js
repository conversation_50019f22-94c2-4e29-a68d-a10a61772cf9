#!/usr/bin/env node

/**
 * n8n Workflow JSON Fixer
 * Automatically fixes common issues in n8n workflow JSON files
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Common node type mappings
const NODE_TYPE_FIXES = {
  '@n8n/n8n-nodes-langchain.openAi': 'n8n-nodes-base.openAi',
  '@n8n/n8n-nodes-langchain.openAiEmbeddings': 'n8n-nodes-base.openAi',
  'n8n-nodes-base.mondayCom': 'n8n-nodes-base.mondaycom',
  // Add more mappings as needed
};

// Required root properties with defaults
const REQUIRED_ROOT_PROPS = {
  active: false,
  settings: { executionOrder: 'v1' },
  staticData: {},
  tags: [],
  triggerCount: 1,
  updatedAt: new Date().toISOString()
};

function generateUUID() {
  return uuidv4();
}

function isValidUUID(str) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

function fixNodeId(nodeId) {
  if (isValidUUID(nodeId)) {
    return nodeId;
  }
  return generateUUID();
}

function fixNodeType(nodeType) {
  return NODE_TYPE_FIXES[nodeType] || nodeType;
}

function addCredentials(node) {
  const credentialMappings = {
    'n8n-nodes-base.openAi': {
      openAiApi: {
        id: generateUUID(),
        name: 'OpenAI API'
      }
    },
    'n8n-nodes-base.googleSheets': {
      googleSheetsOAuth2Api: {
        id: generateUUID(),
        name: 'Google Sheets OAuth2'
      }
    },
    'n8n-nodes-base.gmail': {
      gmailOAuth2: {
        id: generateUUID(),
        name: 'Gmail OAuth2'
      }
    },
    'n8n-nodes-base.telegram': {
      telegramApi: {
        id: generateUUID(),
        name: 'Telegram Bot'
      }
    },
    'n8n-nodes-base.hubspot': {
      hubspotApi: {
        id: generateUUID(),
        name: 'HubSpot API'
      }
    },
    'n8n-nodes-base.pinecone': {
      pineconeApi: {
        id: generateUUID(),
        name: 'Pinecone API'
      }
    },
    'n8n-nodes-base.zendesk': {
      zendeskApi: {
        id: generateUUID(),
        name: 'Zendesk API'
      }
    },
    'n8n-nodes-base.quickbooks': {
      quickBooksOAuth2Api: {
        id: generateUUID(),
        name: 'QuickBooks OAuth2'
      }
    }
  };

  if (credentialMappings[node.type]) {
    node.credentials = credentialMappings[node.type];
  }

  return node;
}

function fixWorkflow(workflow) {
  console.log(`Fixing workflow: ${workflow.name}`);
  
  // Add missing root properties
  Object.keys(REQUIRED_ROOT_PROPS).forEach(prop => {
    if (!workflow.hasOwnProperty(prop)) {
      workflow[prop] = REQUIRED_ROOT_PROPS[prop];
      console.log(`  ✓ Added missing property: ${prop}`);
    }
  });

  // Fix versionId
  if (!workflow.versionId || !isValidUUID(workflow.versionId)) {
    workflow.versionId = generateUUID();
    console.log(`  ✓ Fixed versionId`);
  }

  // Track node ID changes for connection updates
  const nodeIdMap = {};

  // Fix nodes
  if (workflow.nodes) {
    workflow.nodes.forEach((node, index) => {
      const oldId = node.id;
      
      // Fix node ID
      const newId = fixNodeId(node.id);
      if (newId !== oldId) {
        nodeIdMap[oldId] = newId;
        node.id = newId;
        console.log(`  ✓ Fixed node ID: ${oldId} -> ${newId}`);
      }

      // Fix node type
      const oldType = node.type;
      const newType = fixNodeType(node.type);
      if (newType !== oldType) {
        node.type = newType;
        console.log(`  ✓ Fixed node type: ${oldType} -> ${newType}`);
      }

      // Add credentials
      node = addCredentials(node);

      // Ensure position is valid
      if (!node.position || !Array.isArray(node.position) || node.position.length !== 2) {
        node.position = [240 + (index * 220), 300];
        console.log(`  ✓ Fixed position for node: ${node.name}`);
      }

      workflow.nodes[index] = node;
    });
  }

  // Fix connections to use new node IDs
  if (workflow.connections && Object.keys(nodeIdMap).length > 0) {
    const newConnections = {};
    
    Object.keys(workflow.connections).forEach(sourceNodeName => {
      const sourceNode = workflow.nodes.find(n => n.name === sourceNodeName);
      if (sourceNode) {
        const connections = workflow.connections[sourceNodeName];
        
        // Update target node references
        Object.keys(connections).forEach(outputType => {
          connections[outputType].forEach(outputArray => {
            outputArray.forEach(connection => {
              const targetNode = workflow.nodes.find(n => n.name === connection.node);
              if (targetNode) {
                // Connection structure is correct, just verify target exists
                console.log(`  ✓ Verified connection: ${sourceNodeName} -> ${connection.node}`);
              }
            });
          });
        });
        
        newConnections[sourceNodeName] = connections;
      }
    });
    
    workflow.connections = newConnections;
  }

  // Fix tags
  if (workflow.tags && Array.isArray(workflow.tags)) {
    workflow.tags.forEach(tag => {
      if (!tag.id || !isValidUUID(tag.id)) {
        tag.id = generateUUID();
      }
      if (!tag.createdAt) {
        tag.createdAt = new Date().toISOString();
      }
      if (!tag.updatedAt) {
        tag.updatedAt = new Date().toISOString();
      }
    });
  }

  console.log(`  ✅ Workflow fixed successfully\n`);
  return workflow;
}

function processWorkflowFile(filePath) {
  try {
    console.log(`Processing: ${filePath}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    const workflow = JSON.parse(content);
    
    const fixedWorkflow = fixWorkflow(workflow);
    
    // Create backup
    const backupPath = filePath.replace('.json', '.backup.json');
    fs.writeFileSync(backupPath, content);
    
    // Write fixed workflow
    fs.writeFileSync(filePath, JSON.stringify(fixedWorkflow, null, 2));
    
    console.log(`✅ Fixed and saved: ${filePath}`);
    console.log(`📁 Backup created: ${backupPath}\n`);
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function findWorkflowFiles(dir) {
  const files = [];
  
  function scanDir(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDir(fullPath);
      } else if (item.endsWith('.json') && !item.includes('backup') && !item.includes('FIXED')) {
        files.push(fullPath);
      }
    });
  }
  
  scanDir(dir);
  return files;
}

// Main execution
function main() {
  console.log('🔧 n8n Workflow JSON Fixer\n');
  
  const workflowFiles = findWorkflowFiles('.');
  
  if (workflowFiles.length === 0) {
    console.log('No workflow JSON files found.');
    return;
  }
  
  console.log(`Found ${workflowFiles.length} workflow files:\n`);
  
  workflowFiles.forEach(file => {
    processWorkflowFile(file);
  });
  
  console.log('🎉 All workflows processed!');
  console.log('\n📋 Next steps:');
  console.log('1. Test import each workflow into n8n');
  console.log('2. Configure credentials for each integration');
  console.log('3. Test workflow execution');
  console.log('4. Update documentation with any changes');
}

if (require.main === module) {
  main();
}

module.exports = { fixWorkflow, processWorkflowFile };
