{"name": "AI-Powered Lead Capture & Business Q&A Chatbot - FIXED", "active": false, "nodes": [{"parameters": {"path": "lead-capture", "options": {}, "httpMethod": "POST"}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "webhook-uuid-1234"}, {"parameters": {"resource": "chat", "operation": "message", "model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "You are a helpful business assistant for {{ $input.first().json.company_name || 'our company' }}. Your role is to:\n\n1. Answer questions about business hours, services, products, and general company information\n2. Collect lead information when users show interest\n3. Be friendly, professional, and helpful\n\nIf a user shows interest in our services or wants to learn more, collect:\n- Name\n- Email\n- Phone number (optional)\n- Specific interest/need\n\nUser message: {{ $input.first().json.message }}\n\nContext from knowledge base: {{ $('Search Knowledge Base').first() ? $('Search Knowledge Base').first().json.matches.map(match => match.metadata.content).join('\\n\\n') : 'No additional context available' }}\n\nRespond naturally and helpfully. If collecting lead info, ask for one piece of information at a time."}]}, "options": {"temperature": 0.7, "maxTokens": 500}}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "OpenAI Chat", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [700, 300], "credentials": {"openAiApi": {"id": "openai-cred-uuid-1234", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": false}, "conditions": [{"id": "lead-condition-1", "leftValue": "={{ $input.first().json.choices ? $input.first().json.choices[0].message.content.toLowerCase() : $input.first().json.response.toLowerCase() }}", "rightValue": "email", "operation": "contains"}, {"id": "lead-condition-2", "leftValue": "={{ $input.first().json.choices ? $input.first().json.choices[0].message.content.toLowerCase() : $input.first().json.response.toLowerCase() }}", "rightValue": "interested", "operation": "contains"}, {"id": "lead-condition-3", "leftValue": "={{ $input.first().json.choices ? $input.first().json.choices[0].message.content.toLowerCase() : $input.first().json.response.toLowerCase() }}", "rightValue": "contact", "operation": "contains"}], "combineOperation": "any"}}, "id": "c3d4e5f6-g7h8-9012-cdef-************", "name": "Check Lead Interest", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [920, 180]}, {"parameters": {"model": "text-embedding-ada-002", "input": "={{ $input.first().json.message }}"}, "id": "d4e5f6g7-h8i9-0123-defg-************", "name": "Create Message Embedding", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 180], "credentials": {"openAiApi": {"id": "openai-cred-uuid-1234", "name": "OpenAI API"}}}, {"parameters": {"operation": "search", "index": "={{ $input.first().json.knowledge_base_index || 'support-kb' }}", "namespace": "={{ $input.first().json.namespace || 'general' }}", "vector": "={{ $input.first().json.data && $input.first().json.data[0] ? $input.first().json.data[0].embedding : [] }}", "topK": 3, "includeMetadata": true, "includeValues": false}, "id": "e5f6g7h8-i9j0-1234-efgh-************", "name": "Search Knowledge Base", "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [580, 180], "credentials": {"pineconeApi": {"id": "pinecone-cred-uuid-1234", "name": "Pinecone API"}}}, {"parameters": {"operation": "append", "documentId": "={{ $('Webhook Trigger').first().json.leads_sheet_id || '1LeadCapture123' }}", "sheetName": "Leads", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ $now.toISOString() }}", "message": "={{ $('Webhook Trigger').first().json.message }}", "ai_response": "={{ $input.first().json.choices ? $input.first().json.choices[0].message.content : $input.first().json.response }}", "lead_detected": "={{ $('Check Lead Interest').first() ? 'Yes' : 'No' }}", "session_id": "={{ $('Webhook Trigger').first().json.session_id || 'session_' + Date.now() }}", "source": "={{ $('Webhook Trigger').first().json.source || 'website' }}", "user_email": "={{ $('Webhook Trigger').first().json.user_email || '' }}", "user_name": "={{ $('Webhook Trigger').first().json.user_name || '' }}"}}, "options": {}}, "id": "f6g7h8i9-j0k1-2345-fghi-************", "name": "Log to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1140, 300], "credentials": {"googleSheetsOAuth2Api": {"id": "gsheets-cred-uuid-1234", "name": "Google Sheets OAuth2"}}}, {"parameters": {"resource": "message", "operation": "send", "chatId": "={{ $('Webhook Trigger').first().json.telegram_chat_id || '@leads_channel' }}", "text": "🎯 <b>New Lead Detected!</b>\n\n💬 <b>Message:</b> {{ $('Webhook Trigger').first().json.message }}\n🤖 <b>AI Response:</b> {{ $('OpenAI Chat').first().json.choices ? $('OpenAI Chat').first().json.choices[0].message.content : $('OpenAI Chat').first().json.response }}\n📅 <b>Time:</b> {{ $now.toLocaleString() }}\n🔗 <b>Session:</b> {{ $('Webhook Trigger').first().json.session_id || 'Unknown' }}\n📊 <b>Source:</b> {{ $('Webhook Trigger').first().json.source || 'website' }}", "additionalFields": {"parse_mode": "HTML", "disable_web_page_preview": true}}, "id": "g7h8i9j0-k1l2-3456-ghij-************", "name": "Notify Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1140, 180], "credentials": {"telegramApi": {"id": "telegram-cred-uuid-1234", "name": "Telegram Bot"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"response\": $input.first().json.choices ? $input.first().json.choices[0].message.content : $input.first().json.response,\n  \"timestamp\": $now.toISOString(),\n  \"session_id\": $('Webhook Trigger').first().json.session_id || 'session_' + Date.now(),\n  \"lead_detected\": $('Check Lead Interest').first() ? true : false\n} }}", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}]}}}, "id": "h8i9j0k1-l2m3-4567-hijk-************", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1140, 420]}, {"parameters": {"values": {"string": [{"name": "debug_webhook_data", "value": "={{ JSON.stringify($input.first().json, null, 2) }}"}, {"name": "debug_message", "value": "={{ $input.first().json.message || 'No message found' }}"}, {"name": "debug_session_id", "value": "={{ $input.first().json.session_id || 'No session ID' }}"}]}, "options": {}}, "id": "debug-node-uuid-1234", "name": "Debug Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [340, 180]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Debug Data", "type": "main", "index": 0}]]}, "Debug Data": {"main": [[{"node": "Create Message Embedding", "type": "main", "index": 0}]]}, "Create Message Embedding": {"main": [[{"node": "Search Knowledge Base", "type": "main", "index": 0}]]}, "Search Knowledge Base": {"main": [[{"node": "OpenAI Chat", "type": "main", "index": 0}]]}, "OpenAI Chat": {"main": [[{"node": "Check Lead Interest", "type": "main", "index": 0}, {"node": "Log to Google Sheets", "type": "main", "index": 0}, {"node": "Send Response", "type": "main", "index": 0}]]}, "Check Lead Interest": {"main": [[{"node": "Notify Team", "type": "main", "index": 0}], []]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": ""}, "staticData": {}, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "marketing-uuid-1234", "name": "Marketing"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "lead-gen-uuid-5678", "name": "Lead Generation"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-chatbot-uuid-9012", "name": "AI Chatbot"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "workflow-uuid-abcd-1234-5678-9012-fixed"}