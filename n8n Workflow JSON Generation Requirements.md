# N8N Workflow JSON Generation Requirements

## Overview
Generate a valid n8n workflow JSON file that can be imported directly into n8n. The workflow must follow n8n's specific JSON structure and formatting requirements.

## Core JSON Structure Requirements

### 1. Root Level Properties
The JSON must contain these essential top-level properties:
- `name`: String - The workflow name
- `active`: Boolean - Whether the workflow is active (typically `false` for imports)
- `nodes`: Array - All workflow nodes
- `connections`: Object - Node connections/relationships
- `settings`: Object - Workflow settings
- `staticData`: Object - Static workflow data (can be empty `{}`)
- `tags`: Array - Workflow tags (can be empty `[]`)
- `triggerCount`: Number - Number of trigger nodes (typically `0` or `1`)
- `updatedAt`: String - ISO timestamp (use current time)
- `versionId`: String - UUID for versioning

### 2. Node Structure Requirements
Each node in the `nodes` array must have:
- `parameters`: Object - Node-specific configuration
- `id`: String - Unique UUID for the node
- `name`: String - Display name (must be unique within workflow)
- `type`: String - Node type (e.g., "n8n-nodes-base.httpRequest", "n8n-nodes-base.set")
- `typeVersion`: Number - Node type version (typically `1`, `2`, or `3`)
- `position`: Array - Two numbers `[x, y]` for canvas positioning

### 3. Connection Structure Requirements
The `connections` object maps node relationships:
- Keys are source node names
- Values are objects with output types (`main`, `ai_tool`, etc.)
- Each output type contains arrays of connection objects
- Connection objects have: `node` (target node name), `type` (connection type), `index` (output/input index)

### 4. Data Flow Requirements
- Data between nodes must be structured as arrays of objects
- Each data item should have a `json` key containing the actual data
- Support for multiple input/output connections per node

## Node Type Specifications

### Common Node Types to Support:
- **Trigger Nodes**: `manualTrigger`, `webhook`, `cron`, `httpRequest`
- **Data Nodes**: `set`, `code`, `function`, `merge`, `if`
- **Integration Nodes**: `httpRequest`, `gmail`, `slack`, `googleSheets`
- **AI Nodes**: `openAi`, `anthropic`, `llamaIndex`
- **Utility Nodes**: `wait`, `stopAndError`, `noOp`

### Node-Specific Parameter Requirements:
- **HTTP Request**: `url`, `method`, `headers`, `body`, `authentication`
- **Set Node**: `fields` with `name`, `value` pairs
- **Code/Function**: `code` parameter with JavaScript
- **Manual Trigger**: Usually no parameters needed
- **Webhook**: `path`, `httpMethod`, `responseMode`

## Validation Rules

### 1. Structural Validation
- All node IDs must be valid UUIDs
- Node names must be unique within the workflow
- All connections must reference existing node names
- Position coordinates must be reasonable numbers (0-2000 range)

### 2. Logical Validation
- Every non-trigger node must have at least one input connection
- Trigger nodes should not have input connections
- The workflow should have at least one trigger node
- Connection indices must match node input/output capabilities

### 3. Data Consistency
- Node types must use correct typeVersion for their functionality
- Parameters must match the expected schema for each node type
- Credential references should be properly formatted (even if placeholder)

## Security and Credential Handling

### Credential Requirements:
- Include credential names and IDs in exported JSON
- Use placeholder credential names like "httpAuth" or "apiKey"
- Set credential IDs as UUIDs
- Never include actual sensitive data in parameters

### Authentication Parameters:
- For HTTP nodes: Use `authentication` parameter with credential reference
- For service nodes: Use service-specific credential parameters
- Include `genericCredentialType` when applicable

## Output Format Requirements

### JSON Formatting:
- Use proper JSON formatting with appropriate indentation
- Include all required commas and brackets
- Ensure valid escape sequences for strings
- Use double quotes for all string values

### Metadata:
- Include descriptive workflow name
- Add relevant tags for categorization
- Set appropriate timestamps
- Generate valid UUIDs for all ID fields

## Example Workflow Patterns

### Basic HTTP to Processing Pattern:
1. Manual Trigger → HTTP Request → Set/Code → Output
2. Include error handling with conditional branches
3. Add data transformation between steps

### AI Integration Pattern:
1. Trigger → Data Preparation → AI Node → Response Processing
2. Include schema validation for AI inputs/outputs
3. Add structured output parsing

## Testing and Validation

### Pre-Export Checks:
- Validate JSON syntax and structure
- Verify all node connections are valid
- Check for orphaned nodes (nodes without connections)
- Ensure proper data flow through the workflow

### Import Compatibility:
- JSON should import cleanly into n8n without errors
- All node types should be recognized
- Connections should display correctly in the visual editor
- Workflow should be executable (after credential setup)

## Common Pitfalls to Avoid

1. **Invalid UUIDs**: Generate proper UUID v4 format
2. **Circular References**: Ensure no loops in connections unless intentional
3. **Missing Required Parameters**: Each node type has mandatory parameters
4. **Incorrect TypeVersions**: Use current versions for node types
5. **Malformed Connections**: Connection objects must have exact structure
6. **Position Overlaps**: Avoid placing nodes at identical coordinates

## Sources and References
- N8N Documentation: https://docs.n8n.io
- N8N Workflows: https://n8n.io/workflows
- N8N exports workflows in JSON format with specific structure requirements
- Node connections pass data between inputs and outputs with specific connection structure

## Final Output Requirements
Generate a complete, valid n8n workflow JSON that:
- Can be imported via n8n's "Import from File" feature
- Displays correctly in the visual workflow editor  
- Is executable after minimal credential configuration
- Follows all structural and formatting requirements above
- Includes meaningful node names and descriptions
- Has logical data flow and error handling where appropriate