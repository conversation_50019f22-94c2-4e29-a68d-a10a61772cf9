{"name": "AI-Powered Customer Support Chatbot with Ticket Creation", "nodes": [{"parameters": {"path": "support-chat", "options": {}}, "id": "webhook-trigger", "name": "Support Chat Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"model": "text-embedding-ada-002", "input": "={{ $json.message }}"}, "id": "create-embedding", "name": "Create Message Embedding", "type": "@n8n/n8n-nodes-langchain.openAiEmbeddings", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"operation": "search", "index": "{{ $json.knowledge_base_index || 'support-kb' }}", "namespace": "{{ $json.namespace || 'general' }}", "vector": "={{ $('Create Message Embedding').item.json.embedding }}", "topK": 5, "includeMetadata": true, "filter": {"category": "{{ $json.category || 'general' }}"}}, "id": "search-knowledge-base", "name": "Search Knowledge Base", "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "You are a helpful customer support agent. Based on this knowledge base information:\n\n{{ $('Search Knowledge Base').item.json.matches.map(m => `${m.metadata.title}: ${m.metadata.content}`).join('\\n\\n') }}\n\nCustomer Information:\n- Name: {{ $json.customer_name || 'Customer' }}\n- Email: {{ $json.customer_email || 'Not provided' }}\n- Account Type: {{ $json.account_type || 'Unknown' }}\n- Previous Tickets: {{ $json.previous_tickets || 'None' }}\n\nCustomer Message: {{ $json.message }}\n\nProvide a helpful, accurate response. If you can resolve the issue, provide step-by-step instructions. If you cannot resolve it or need more information, indicate that a support ticket should be created.\n\nRespond in this JSON format:\n{\n  \"response\": \"Your helpful response to the customer\",\n  \"confidence\": 0.85,\n  \"resolution_status\": \"resolved\" or \"needs_ticket\" or \"needs_escalation\",\n  \"category\": \"technical\", \"billing\", \"general\", or \"product\",\n  \"urgency\": \"low\", \"medium\", \"high\", or \"critical\"\n}"}]}, "options": {"temperature": 0.3, "maxTokens": 500}}, "id": "ai-support-response", "name": "Generate AI Support Response", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "notEqual"}, "conditions": [{"leftValue": "={{ JSON.parse($('Generate AI Support Response').item.json.response).resolution_status }}", "rightValue": "resolved", "operation": "notEqual"}], "combineOperation": "any"}}, "id": "check-needs-ticket", "name": "Check if Ticket Needed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"resource": "ticket", "operation": "create", "subject": "{{ JSON.parse($('Generate AI Support Response').item.json.response).category.toUpperCase() }}: {{ $json.message.substring(0, 50) }}...", "description": "Customer Message: {{ $json.message }}\\n\\nCustomer Information:\\n- Name: {{ $json.customer_name }}\\n- Email: {{ $json.customer_email }}\\n- Account Type: {{ $json.account_type }}\\n\\nAI Analysis:\\n- Category: {{ JSON.parse($('Generate AI Support Response').item.json.response).category }}\\n- Urgency: {{ JSON.parse($('Generate AI Support Response').item.json.response).urgency }}\\n- AI Response: {{ JSON.parse($('Generate AI Support Response').item.json.response).response }}\\n\\nThis ticket was automatically created by the AI support system.", "additionalFields": {"priority": "={{ JSON.parse($('Generate AI Support Response').item.json.response).urgency === 'critical' ? 'urgent' : JSON.parse($('Generate AI Support Response').item.json.response).urgency === 'high' ? 'high' : JSON.parse($('Generate AI Support Response').item.json.response).urgency === 'medium' ? 'normal' : 'low' }}", "status": "new", "type": "{{ JSON.parse($('Generate AI Support Response').item.json.response).category }}", "tags": ["ai-created", "{{ JSON.parse($('Generate AI Support Response').item.json.response).category }}"], "customFields": {"ai_confidence": "{{ JSON.parse($('Generate AI Support Response').item.json.response).confidence }}", "ai_category": "{{ JSON.parse($('Generate AI Support Response').item.json.response).category }}", "customer_account_type": "{{ $json.account_type }}"}}, "requesterId": "={{ $json.customer_email }}"}, "id": "create-zendesk-ticket", "name": "Create Zendesk Ticket", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [1340, 100]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.support_team_chat || '@support_team' }}", "text": "🎫 New Support Ticket Created\n\n👤 Customer: {{ $json.customer_name }} ({{ $json.customer_email }})\n📝 Subject: {{ JSON.parse($('Generate AI Support Response').item.json.response).category.toUpperCase() }}: {{ $json.message.substring(0, 50) }}...\n🚨 Priority: {{ JSON.parse($('Generate AI Support Response').item.json.response).urgency.toUpperCase() }}\n📊 AI Confidence: {{ (JSON.parse($('Generate AI Support Response').item.json.response).confidence * 100).toFixed(0) }}%\n🎯 Category: {{ JSON.parse($('Generate AI Support Response').item.json.response).category }}\n\n💬 Customer Message: {{ $json.message }}\n\n🤖 AI Response: {{ JSON.parse($('Generate AI Support Response').item.json.response).response }}\n\n🔗 Ticket ID: {{ $('Create Zendesk Ticket').item.json.id }}", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-support-team", "name": "Notify Support Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1560, 100]}, {"parameters": {"operation": "append", "documentId": "{{ $json.support_analytics_sheet || '1SupportAnalytics123' }}", "sheetName": "Support Interactions", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ new Date().toISOString() }}", "customer_email": "={{ $json.customer_email }}", "customer_name": "={{ $json.customer_name }}", "message": "={{ $json.message }}", "ai_response": "={{ JSON.parse($('Generate AI Support Response').item.json.response).response }}", "resolution_status": "={{ JSON.parse($('Generate AI Support Response').item.json.response).resolution_status }}", "category": "={{ JSON.parse($('Generate AI Support Response').item.json.response).category }}", "urgency": "={{ JSON.parse($('Generate AI Support Response').item.json.response).urgency }}", "confidence": "={{ JSON.parse($('Generate AI Support Response').item.json.response).confidence }}", "ticket_created": "={{ $('Check if Ticket Needed').item.json ? 'Yes' : 'No' }}", "ticket_id": "={{ $('Create Zendesk Ticket').item.json.id || 'N/A' }}"}}}, "id": "log-interaction", "name": "Log Support Interaction", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1340, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": JSON.parse($('Generate AI Support Response').item.json.response).response,\n  \"resolution_status\": JSON.parse($('Generate AI Support Response').item.json.response).resolution_status,\n  \"ticket_id\": $('Create Zendesk Ticket').item.json.id || null,\n  \"confidence\": JSON.parse($('Generate AI Support Response').item.json.response).confidence,\n  \"category\": JSON.parse($('Generate AI Support Response').item.json.response).category,\n  \"timestamp\": new Date().toISOString()\n} }}"}, "id": "send-response", "name": "Send Response to Customer", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Analyze this customer support interaction for sentiment and satisfaction prediction:\n\nCustomer Message: {{ $json.message }}\nAI Response: {{ JSON.parse($('Generate AI Support Response').item.json.response).response }}\nResolution Status: {{ JSON.parse($('Generate AI Support Response').item.json.response).resolution_status }}\n\nPredict:\n1. Customer sentiment (positive, neutral, negative)\n2. Likelihood of satisfaction (0-1)\n3. Risk of churn (low, medium, high)\n4. Recommended follow-up action\n\nReturn JSON:\n{\n  \"sentiment\": \"positive/neutral/negative\",\n  \"satisfaction_score\": 0.85,\n  \"churn_risk\": \"low/medium/high\",\n  \"follow_up_action\": \"recommended action\"\n}"}]}, "options": {"temperature": 0.2, "maxTokens": 200}}, "id": "analyze-satisfaction", "name": "Analyze Customer Satisfaction", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1340, 600]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "equal"}, "conditions": [{"leftValue": "={{ JSON.parse($('Analyze Customer Satisfaction').item.json.response).churn_risk }}", "rightValue": "high", "operation": "equal"}], "combineOperation": "any"}}, "id": "check-churn-risk", "name": "Check High Churn Risk", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 600]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.customer_success_chat || '@customer_success' }}", "text": "⚠️ HIGH CHURN RISK ALERT\n\n👤 Customer: {{ $json.customer_name }} ({{ $json.customer_email }})\n😟 Sentiment: {{ JSON.parse($('Analyze Customer Satisfaction').item.json.response).sentiment.toUpperCase() }}\n📉 Satisfaction Score: {{ (JSON.parse($('Analyze Customer Satisfaction').item.json.response).satisfaction_score * 100).toFixed(0) }}%\n🚨 Churn Risk: HIGH\n\n💬 Original Issue: {{ $json.message }}\n🤖 AI Response: {{ JSON.parse($('Generate AI Support Response').item.json.response).response }}\n\n🎯 Recommended Action: {{ JSON.parse($('Analyze Customer Satisfaction').item.json.response).follow_up_action }}\n\n⚡ IMMEDIATE ATTENTION REQUIRED!", "additionalFields": {"parse_mode": "HTML"}}, "id": "alert-customer-success", "name": "<PERSON><PERSON> Customer Success Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1780, 600]}], "connections": {"Support Chat Webhook": {"main": [[{"node": "Create Message Embedding", "type": "main", "index": 0}]]}, "Create Message Embedding": {"main": [[{"node": "Search Knowledge Base", "type": "main", "index": 0}]]}, "Search Knowledge Base": {"main": [[{"node": "Generate AI Support Response", "type": "main", "index": 0}]]}, "Generate AI Support Response": {"main": [[{"node": "Check if Ticket Needed", "type": "main", "index": 0}, {"node": "Log Support Interaction", "type": "main", "index": 0}, {"node": "Analyze Customer Satisfaction", "type": "main", "index": 0}]]}, "Check if Ticket Needed": {"main": [[{"node": "Create Zendesk Ticket", "type": "main", "index": 0}], [{"node": "Send Response to Customer", "type": "main", "index": 0}]]}, "Create Zendesk Ticket": {"main": [[{"node": "Notify Support Team", "type": "main", "index": 0}]]}, "Notify Support Team": {"main": [[{"node": "Send Response to Customer", "type": "main", "index": 0}]]}, "Log Support Interaction": {"main": [[{"node": "Send Response to Customer", "type": "main", "index": 0}]]}, "Analyze Customer Satisfaction": {"main": [[{"node": "Check High Churn Risk", "type": "main", "index": 0}]]}, "Check High Churn Risk": {"main": [[{"node": "<PERSON><PERSON> Customer Success Team", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "customer-support", "name": "Customer Support"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-chatbot", "name": "AI Chatbot"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ticket-automation", "name": "Ticket Automation"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "1"}