# 🚀 n8n Startup Workflows - Complete Setup Guide

This guide will help you set up and configure all the n8n workflows for your startup automation needs.

## 📋 Prerequisites

### 1. n8n Installation
- **Cloud**: Sign up at [n8n.cloud](https://n8n.cloud) (recommended for beginners)
- **Self-hosted**: Follow [installation guide](https://docs.n8n.io/hosting/)
- **Docker**: `docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n`

### 2. Required Accounts & API Keys

#### Essential Integrations
- **OpenAI**: API key for GPT-4/GPT-3.5 ([platform.openai.com](https://platform.openai.com))
- **Google Workspace**: OAuth2 for Gmail, Sheets, Drive
- **Telegram**: Bot token for notifications ([BotFather](https://t.me/botfather))

#### CRM & Sales
- **HubSpot**: Private app access token
- **Salesforce**: Connected app credentials (optional)
- **Pipedrive**: API token (optional)

#### Finance & Accounting
- **QuickBooks**: OAuth2 app credentials
- **Stripe**: API keys for payment processing
- **PayPal**: API credentials (optional)

#### Support & Communication
- **Zendesk**: API token and subdomain
- **Slack**: Bot token and workspace access
- **Discord**: Bot token (optional)

#### Data & Analytics
- **Pinecone**: API key for vector database
- **Clearbit**: API key for lead enrichment
- **Jina.ai**: API key for website analysis

## 🔧 Step-by-Step Setup

### Step 1: Import Workflows

1. **Download workflow files** from this repository
2. **Open n8n** in your browser
3. **Click "Import from file"** in the workflows section
4. **Select JSON files** one by one
5. **Save each workflow** after import

### Step 2: Configure Credentials

#### OpenAI Setup
1. Go to **Settings > Credentials**
2. Click **"Add Credential"**
3. Select **"OpenAI"**
4. Enter your **API key**
5. Test the connection

#### Google Services Setup
1. Create **Google Cloud Project**
2. Enable **Gmail API, Sheets API, Drive API**
3. Create **OAuth2 credentials**
4. Configure **redirect URIs**
5. Add credentials to n8n

#### Telegram Bot Setup
1. Message **@BotFather** on Telegram
2. Create new bot with `/newbot`
3. Copy the **bot token**
4. Add token to n8n credentials
5. Get your **chat ID** for notifications

### Step 3: Customize Workflows

#### Marketing Workflows
- **Update company information** in AI prompts
- **Configure lead scoring criteria**
- **Set up Google Sheets** for lead tracking
- **Customize email templates**

#### Sales Workflows
- **Configure CRM field mappings**
- **Set up lead routing rules**
- **Customize scoring algorithms**
- **Configure notification channels**

#### Support Workflows
- **Set up knowledge base** in Pinecone
- **Configure ticket categories**
- **Customize AI responses**
- **Set up escalation rules**

#### Finance Workflows
- **Configure QuickBooks integration**
- **Set up customer data sheets**
- **Customize invoice templates**
- **Configure payment webhooks**

### Step 4: Test Workflows

1. **Start with manual triggers**
2. **Use sample data** for testing
3. **Check all integrations** work correctly
4. **Verify notifications** are sent
5. **Test error handling**

### Step 5: Go Live

1. **Activate scheduled triggers**
2. **Set up webhook endpoints**
3. **Monitor workflow executions**
4. **Set up error alerts**
5. **Train your team**

## 📊 Workflow Configuration

### High-Priority Workflows (Start Here)

#### 1. AI Lead Capture Chatbot
- **File**: `02-marketing/ai-lead-capture-chatbot.json`
- **Setup Time**: 30 minutes
- **Impact**: Immediate lead generation
- **Requirements**: OpenAI, Google Sheets, Telegram

#### 2. AI Lead Scoring & Routing
- **File**: `03-sales/ai-lead-scoring.json`
- **Setup Time**: 45 minutes
- **Impact**: Better lead qualification
- **Requirements**: OpenAI, HubSpot, Clearbit, Telegram

#### 3. Customer Support Chatbot
- **File**: `04-customer-support/ai-support-chatbot.json`
- **Setup Time**: 60 minutes
- **Impact**: 24/7 customer support
- **Requirements**: OpenAI, Pinecone, Zendesk, Telegram

#### 4. Automated Invoicing
- **File**: `05-finance-accounting/automated-invoicing.json`
- **Setup Time**: 90 minutes
- **Impact**: Streamlined billing
- **Requirements**: QuickBooks, Gmail, Google Sheets

### Medium-Priority Workflows

#### 5. Smart Email Outreach
- **File**: `02-marketing/smart-email-outreach.json`
- **Setup Time**: 45 minutes
- **Impact**: Personalized outreach
- **Requirements**: OpenAI, Gmail, Jina.ai

## 🔗 Integration Details

### Google Sheets Setup
1. **Create master sheets** for each workflow
2. **Set up proper headers** as shown in examples
3. **Share sheets** with your n8n service account
4. **Copy sheet IDs** to workflow configurations

### Pinecone Knowledge Base
1. **Create index** for support knowledge
2. **Upload company documentation**
3. **Create embeddings** for search
4. **Test search functionality**

### HubSpot Configuration
1. **Create custom properties** for lead scoring
2. **Set up workflows** for automation
3. **Configure webhooks** for real-time updates
4. **Map data fields** correctly

### Telegram Notifications
1. **Create team channels** for different functions
2. **Add your bot** to each channel
3. **Get channel IDs** (use @userinfobot)
4. **Test notifications**

## 🎯 Customization Guide

### AI Prompts
- **Adjust tone and voice** to match your brand
- **Add specific product information**
- **Include company policies**
- **Customize response templates**

### Lead Scoring
- **Define your ideal customer profile**
- **Set scoring weights** for different criteria
- **Configure routing rules**
- **Set up escalation thresholds**

### Email Templates
- **Add your branding**
- **Customize subject lines**
- **Include relevant CTAs**
- **Set up tracking**

### Notification Preferences
- **Choose appropriate channels**
- **Set urgency levels**
- **Configure quiet hours**
- **Customize message formats**

## 🔍 Troubleshooting

### Common Issues

#### API Rate Limits
- **Add delays** between requests
- **Implement retry logic**
- **Use batch processing**
- **Monitor usage**

#### Authentication Errors
- **Check token expiration**
- **Refresh OAuth tokens**
- **Verify permissions**
- **Test connections**

#### Data Format Issues
- **Validate input data**
- **Handle missing fields**
- **Convert data types**
- **Add error handling**

### Performance Optimization
- **Use conditional logic** to reduce processing
- **Implement caching** where possible
- **Optimize database queries**
- **Monitor execution times**

## 📈 Success Metrics

### Marketing
- **Lead capture rate**: Increase in qualified leads
- **Email open rates**: Improvement in engagement
- **Response rates**: Higher reply rates
- **Cost per lead**: Reduction in acquisition costs

### Sales
- **Lead response time**: Faster follow-up
- **Conversion rates**: Higher close rates
- **Pipeline velocity**: Faster deal progression
- **Data quality**: More complete records

### Support
- **Response time**: Faster initial responses
- **Resolution rate**: More issues resolved automatically
- **Customer satisfaction**: Higher CSAT scores
- **Agent productivity**: More time for complex issues

### Finance
- **Invoice processing time**: Faster billing
- **Payment collection**: Improved cash flow
- **Error reduction**: Fewer manual mistakes
- **Compliance**: Better record keeping

## 🎓 Training & Adoption

### Team Training
1. **Demo workflows** to each team
2. **Explain benefits** and impact
3. **Provide documentation**
4. **Set up feedback channels**
5. **Monitor adoption**

### Best Practices
- **Start small** and expand gradually
- **Monitor performance** regularly
- **Gather feedback** from users
- **Iterate and improve**
- **Document changes**

## 🔄 Maintenance

### Regular Tasks
- **Monitor workflow executions**
- **Check error logs**
- **Update API credentials**
- **Review performance metrics**
- **Update AI prompts**

### Monthly Reviews
- **Analyze automation impact**
- **Identify optimization opportunities**
- **Update workflows** as needed
- **Train new team members**
- **Plan new automations**

---

**Ready to transform your startup with automation?** Start with the high-priority workflows and gradually expand your automation ecosystem. Remember: automation is a journey, not a destination!
