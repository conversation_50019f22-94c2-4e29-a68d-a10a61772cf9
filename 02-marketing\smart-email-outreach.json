{"name": "Smart Email Outreach Sequence - AI-Powered", "nodes": [{"parameters": {"operation": "read", "documentId": "{{ $json.sheet_id }}", "sheetName": "Leads", "options": {"range": "A:Z"}}, "id": "read-leads", "name": "Read Leads from Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [240, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "equal"}, "conditions": [{"leftValue": "={{ $json.email_sent }}", "rightValue": "", "operation": "equal"}], "combineOperation": "any"}}, "id": "filter-unsent", "name": "<PERSON><PERSON> Emails", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Extract domain from email\nconst email = $input.item.json.email;\nconst domain = email.split('@')[1];\n\n// Create website URL\nconst websiteUrl = `https://${domain}`;\n\nreturn {\n  ...items[0].json,\n  domain: domain,\n  website_url: websiteUrl\n};"}, "id": "extract-domain", "name": "Extract Domain", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"url": "https://r.jina.ai/{{ $json.website_url }}", "options": {"timeout": 10000, "retry": {"enabled": true, "maxRetries": 2}}}, "id": "analyze-website", "name": "Analyze Website with Jina.ai", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"model": "gpt-3.5-turbo", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Based on this website analysis:\n\n{{ $('Analyze Website with Jina.ai').item.json.content || 'Website content not available' }}\n\nAnd this lead information:\n- Name: {{ $json.name }}\n- Email: {{ $json.email }}\n- Company: {{ $json.domain }}\n- Interest: {{ $json.interest || 'General inquiry' }}\n\nWrite a personalized cold email that:\n1. References something specific about their company/website\n2. Clearly states our value proposition\n3. Includes a soft call-to-action\n4. Keeps it under 150 words\n5. Sounds natural and human\n\nSubject line should be compelling and personalized.\n\nReturn in this JSON format:\n{\n  \"subject\": \"Your subject line\",\n  \"body\": \"Your email body\"\n}"}]}, "options": {"temperature": 0.7, "maxTokens": 400}}, "id": "generate-email", "name": "Generate Personalized Email", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"sendTo": "={{ $json.email }}", "subject": "={{ JSON.parse($('Generate Personalized Email').item.json.response).subject }}", "message": "={{ JSON.parse($('Generate Personalized Email').item.json.response).body }}", "options": {"ccList": "", "bccList": "", "replyTo": "{{ $json.reply_to_email || '<EMAIL>' }}"}}, "id": "send-email", "name": "Send Email via Gmail", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"operation": "update", "documentId": "={{ $('Read Leads from Google Sheets').item.json.sheet_id }}", "sheetName": "Leads", "columnToMatchOn": "email", "valueToMatchOn": "={{ $json.email }}", "columns": {"mappingMode": "defineBelow", "value": {"email_sent": "={{ new Date().toISOString() }}", "email_subject": "={{ JSON.parse($('Generate Personalized Email').item.json.response).subject }}", "outreach_status": "Initial Email <PERSON>t"}}}, "id": "update-status", "name": "Update Lead Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1560, 200]}, {"parameters": {"unit": "seconds", "amount": "={{ Math.floor(Math.random() * 60) + 30 }}"}, "id": "random-delay", "name": "<PERSON> (30-90s)", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 400]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.notification_chat_id || '@marketing_team' }}", "text": "📧 Email Sent Successfully!\n\n👤 To: {{ $json.name }} ({{ $json.email }})\n🏢 Company: {{ $json.domain }}\n📝 Subject: {{ JSON.parse($('Generate Personalized Email').item.json.response).subject }}\n🕒 Time: {{ new Date().toLocaleString() }}\n\n✅ Lead status updated in Google Sheets", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-success", "name": "Notify Team of Success", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"cronExpression": "0 9 * * 1-5", "triggerAtStartup": false}, "id": "schedule-trigger", "name": "Schedule Trigger (9 AM, Mon-Fri)", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [40, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "isNotEmpty"}, "conditions": [{"leftValue": "={{ $('Analyze Website with Jina.ai').item.json.content }}", "rightValue": "", "operation": "isNotEmpty"}], "combineOperation": "any"}}, "id": "check-website-data", "name": "Check Website Data Available", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"model": "gpt-3.5-turbo", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Write a personalized cold email for this lead:\n\n- Name: {{ $json.name }}\n- Email: {{ $json.email }}\n- Company: {{ $json.domain }}\n- Interest: {{ $json.interest || 'General inquiry' }}\n\nSince we couldn't analyze their website, focus on:\n1. Their industry/domain name insights\n2. Our general value proposition\n3. A compelling reason to connect\n4. Keep it under 150 words\n5. Sound natural and human\n\nReturn in this JSON format:\n{\n  \"subject\": \"Your subject line\",\n  \"body\": \"Your email body\"\n}"}]}, "options": {"temperature": 0.7, "maxTokens": 400}}, "id": "generate-fallback-email", "name": "Generate Fallback Email", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Schedule Trigger (9 AM, Mon-Fri)": {"main": [[{"node": "Read Leads from Google Sheets", "type": "main", "index": 0}]]}, "Read Leads from Google Sheets": {"main": [[{"node": "<PERSON><PERSON> Emails", "type": "main", "index": 0}]]}, "Filter Unsent Emails": {"main": [[{"node": "Extract Domain", "type": "main", "index": 0}]]}, "Extract Domain": {"main": [[{"node": "Analyze Website with Jina.ai", "type": "main", "index": 0}]]}, "Analyze Website with Jina.ai": {"main": [[{"node": "Check Website Data Available", "type": "main", "index": 0}]]}, "Check Website Data Available": {"main": [[{"node": "Generate Personalized Email", "type": "main", "index": 0}], [{"node": "Generate Fallback Email", "type": "main", "index": 0}]]}, "Generate Personalized Email": {"main": [[{"node": "Send Email via Gmail", "type": "main", "index": 0}]]}, "Generate Fallback Email": {"main": [[{"node": "<PERSON> (30-90s)", "type": "main", "index": 0}]]}, "Send Email via Gmail": {"main": [[{"node": "Update Lead Status", "type": "main", "index": 0}]]}, "Random Delay (30-90s)": {"main": [[{"node": "Send Email via Gmail", "type": "main", "index": 0}]]}, "Update Lead Status": {"main": [[{"node": "Notify Team of Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "marketing", "name": "Marketing"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "email-outreach", "name": "<PERSON><PERSON>"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-personalization", "name": "AI Personalization"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "1"}