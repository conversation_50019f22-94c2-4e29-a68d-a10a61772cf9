{"name": "AI-Powered Lead Capture & Business Q&A Chatbot", "active": false, "nodes": [{"parameters": {"path": "lead-capture", "options": {}}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "webhook-uuid-1234"}, {"parameters": {"resource": "chat", "operation": "message", "model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "You are a helpful business assistant for {{ $json.company_name || 'our company' }}. Your role is to:\n\n1. Answer questions about business hours, services, products, and general company information\n2. Collect lead information when users show interest\n3. Be friendly, professional, and helpful\n\nIf a user shows interest in our services or wants to learn more, collect:\n- Name\n- Email\n- Phone number (optional)\n- Specific interest/need\n\nUser message: {{ $json.message }}\n\nRespond naturally and helpfully. If collecting lead info, ask for one piece of information at a time."}]}, "options": {"temperature": 0.7, "maxTokens": 500}}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "OpenAI Chat", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [460, 300], "credentials": {"openAiApi": {"id": "openai-cred-uuid-1234", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "operation": "contains"}, "conditions": [{"leftValue": "={{ $('OpenAI Chat').item.json.choices[0].message.content }}", "rightValue": "email", "operation": "contains"}], "combineOperation": "any"}}, "id": "c3d4e5f6-g7h8-9012-cdef-************", "name": "Check Lead Interest", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"model": "text-embedding-ada-002", "input": "={{ $json.message }}"}, "id": "d4e5f6g7-h8i9-0123-defg-************", "name": "Create Message Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [460, 500], "credentials": {"openAiApi": {"id": "openai-cred-uuid-1234", "name": "OpenAI API"}}}, {"parameters": {"operation": "search", "index": "{{ $json.knowledge_base_index || 'support-kb' }}", "namespace": "{{ $json.namespace || 'general' }}", "vector": "={{ $('Create Message Embedding').item.json.data[0].embedding }}", "topK": 3, "includeMetadata": true}, "id": "e5f6g7h8-i9j0-1234-efgh-************", "name": "Search Knowledge Base", "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [680, 500], "credentials": {"pineconeApi": {"id": "pinecone-cred-uuid-1234", "name": "Pinecone API"}}}, {"parameters": {"operation": "append", "documentId": "{{ $json.leads_sheet_id || '1LeadCapture123' }}", "sheetName": "Leads", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ new Date().toISOString() }}", "message": "={{ $json.message }}", "ai_response": "={{ $('OpenAI Chat').item.json.choices[0].message.content }}", "lead_detected": "={{ $('Check Lead Interest').item.json ? 'Yes' : 'No' }}", "session_id": "={{ $json.session_id || 'unknown' }}", "source": "{{ $json.source || 'website' }}"}}}, "id": "f6g7h8i9-j0k1-2345-fghi-************", "name": "Log to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [900, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "gsheets-cred-uuid-1234", "name": "Google Sheets OAuth2"}}}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.telegram_chat_id || '@leads_channel' }}", "text": "🎯 New Lead Detected!\n\n💬 Message: {{ $json.message }}\n🤖 AI Response: {{ $('OpenAI Chat').item.json.choices[0].message.content }}\n📅 Time: {{ new Date().toLocaleString() }}\n🔗 Session: {{ $json.session_id }}\n📊 Source: {{ $json.source }}", "additionalFields": {"parse_mode": "HTML"}}, "id": "g7h8i9j0-k1l2-3456-ghij-************", "name": "Notify Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1120, 100], "credentials": {"telegramApi": {"id": "telegram-cred-uuid-1234", "name": "Telegram Bot"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $('OpenAI Chat').item.json.choices[0].message.content,\n  \"timestamp\": new Date().toISOString(),\n  \"session_id\": $json.session_id\n} }}"}, "id": "h8i9j0k1-l2m3-4567-hijk-************", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "OpenAI Chat", "type": "main", "index": 0}, {"node": "Create Message Embedding", "type": "main", "index": 0}]]}, "OpenAI Chat": {"main": [[{"node": "Check Lead Interest", "type": "main", "index": 0}, {"node": "Log to Google Sheets", "type": "main", "index": 0}, {"node": "Send Response", "type": "main", "index": 0}]]}, "Check Lead Interest": {"main": [[{"node": "Notify Team", "type": "main", "index": 0}]]}, "Create Message Embedding": {"main": [[{"node": "Search Knowledge Base", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "marketing-uuid-1234", "name": "Marketing"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "lead-gen-uuid-5678", "name": "Lead Generation"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-chatbot-uuid-9012", "name": "AI Chatbot"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "workflow-uuid-abcd-1234-5678-9012"}