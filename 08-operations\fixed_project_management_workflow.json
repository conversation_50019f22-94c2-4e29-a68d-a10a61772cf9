{"name": "Project Management & Task Automation - FIXED", "nodes": [{"parameters": {"path": "project-update", "options": {}, "httpMethod": "POST"}, "id": "project-webhook", "name": "Project Update Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Analyze this project update and provide insights:\n\nProject: {{ $input.first().json.project_name }}\nUpdate: {{ $input.first().json.update_text }}\nStatus: {{ $input.first().json.status }}\nProgress: {{ $input.first().json.progress_percent }}%\nTeam: {{ $input.first().json.team_members }}\nDeadline: {{ $input.first().json.deadline }}\n\nProvide analysis in JSON format:\n{\n  \"risk_level\": \"low/medium/high\",\n  \"completion_prediction\": \"on_time/delayed/at_risk\",\n  \"key_insights\": \"Brief analysis\",\n  \"recommended_actions\": [\"action1\", \"action2\"],\n  \"stakeholder_update\": \"Executive summary for stakeholders\",\n  \"next_milestones\": [\"milestone1\", \"milestone2\"]\n}"}]}, "options": {"temperature": 0.3, "maxTokens": 400}}, "id": "analyze-project", "name": "AI Project Analysis", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"resource": "item", "operation": "update", "boardId": "{{ $('Project Update Webhook').first().json.monday_board_id }}", "itemId": "{{ $('Project Update Webhook').first().json.monday_item_id }}", "columnValues": {"status": "{{ $('Project Update Webhook').first().json.status }}", "progress": "{{ $('Project Update Webhook').first().json.progress_percent }}", "ai_risk_level": "{{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).risk_level : JSON.parse($input.first().json.response).risk_level }}", "completion_prediction": "{{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).completion_prediction : JSON.parse($input.first().json.response).completion_prediction }}", "last_update": "{{ $now.toISOString().split('T')[0] }}"}}, "id": "update-monday", "name": "Update Monday.com Board", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true}, "conditions": [{"id": "high-risk-condition", "leftValue": "={{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).risk_level : JSON.parse($input.first().json.response).risk_level }}", "rightValue": "high", "operation": "equal"}], "combineOperation": "any"}}, "id": "check-high-risk", "name": "Check High Risk", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $('Project Update Webhook').first().json.management_chat || '@management_team' }}", "text": "🚨 <b>HIGH RISK PROJECT ALERT</b>\n\n📋 <b>Project:</b> {{ $('Project Update Webhook').first().json.project_name }}\n⚠️ <b>Risk Level:</b> {{ ($('AI Project Analysis').first().json.choices ? JSON.parse($('AI Project Analysis').first().json.choices[0].message.content).risk_level : JSON.parse($('AI Project Analysis').first().json.response).risk_level).toUpperCase() }}\n📊 <b>Progress:</b> {{ $('Project Update Webhook').first().json.progress_percent }}%\n🎯 <b>Status:</b> {{ $('Project Update Webhook').first().json.status }}\n📅 <b>Deadline:</b> {{ $('Project Update Webhook').first().json.deadline }}\n\n🔍 <b>AI Analysis:</b> {{ $('AI Project Analysis').first().json.choices ? JSON.parse($('AI Project Analysis').first().json.choices[0].message.content).key_insights : JSON.parse($('AI Project Analysis').first().json.response).key_insights }}\n\n🎬 <b>Recommended Actions:</b>\n{{ ($('AI Project Analysis').first().json.choices ? JSON.parse($('AI Project Analysis').first().json.choices[0].message.content).recommended_actions : JSON.parse($('AI Project Analysis').first().json.response).recommended_actions).map(action => `• ${action}`).join('\\n') }}\n\n⚡ <b>IMMEDIATE ATTENTION REQUIRED!</b>", "additionalFields": {"parse_mode": "HTML"}}, "id": "alert-management", "name": "Alert Management Team", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 100]}, {"parameters": {"operation": "append", "documentId": "{{ $('Project Update Webhook').first().json.project_tracking_sheet || '1ProjectTracking123' }}", "sheetName": "Project Updates", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ $now.toISOString() }}", "project_name": "={{ $('Project Update Webhook').first().json.project_name }}", "status": "={{ $('Project Update Webhook').first().json.status }}", "progress_percent": "={{ $('Project Update Webhook').first().json.progress_percent }}", "team_members": "={{ $('Project Update Webhook').first().json.team_members }}", "deadline": "={{ $('Project Update Webhook').first().json.deadline }}", "update_text": "={{ $('Project Update Webhook').first().json.update_text }}", "risk_level": "={{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).risk_level : JSON.parse($input.first().json.response).risk_level }}", "completion_prediction": "={{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).completion_prediction : JSON.parse($input.first().json.response).completion_prediction }}", "ai_insights": "={{ $input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).key_insights : JSON.parse($input.first().json.response).key_insights }}", "recommended_actions": "={{ ($input.first().json.choices ? JSON.parse($input.first().json.choices[0].message.content).recommended_actions : JSON.parse($input.first().json.response).recommended_actions).join('; ') }}"}}}, "id": "log-project-update", "name": "Log Project Update", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [900, 400]}, {"parameters": {"cronExpression": "0 9 * * 1", "triggerAtStartup": false}, "id": "weekly-report-trigger", "name": "Weekly Report Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1.1, "position": [240, 600]}, {"parameters": {"operation": "read", "documentId": "{{ $input.first().json.project_tracking_sheet || '1ProjectTracking123' }}", "sheetName": "Project Updates", "options": {"range": "A:Z"}}, "id": "read-project-data", "name": "Read Project Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 600]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Generate a comprehensive weekly project report based on this data:\n\n{{ $input.first().json ? JSON.stringify($input.first().json, null, 2) : 'No project data available' }}\n\nCreate an executive summary including:\n1. Overall project health\n2. Key achievements this week\n3. Projects at risk\n4. Resource allocation insights\n5. Upcoming milestones\n6. Recommended actions\n\nFormat as a professional report suitable for stakeholders."}]}, "options": {"temperature": 0.4, "maxTokens": 800}}, "id": "generate-weekly-report", "name": "Generate Weekly Report", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"sendTo": "{{ $input.first().json.stakeholder_emails || '<EMAIL>' }}", "subject": "Weekly Project Report - {{ $now.toLocaleDateString() }}", "message": "{{ $input.first().json.choices ? $input.first().json.choices[0].message.content : $input.first().json.response }}", "options": {"ccList": "{{ $input.first().json.project_managers_email || '<EMAIL>' }}", "replyTo": "{{ $input.first().json.operations_email || '<EMAIL>' }}"}}, "id": "send-weekly-report", "name": "Send Weekly Report", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [900, 600]}], "connections": {"Project Update Webhook": {"main": [[{"node": "AI Project Analysis", "type": "main", "index": 0}]]}, "AI Project Analysis": {"main": [[{"node": "Update Monday.com Board", "type": "main", "index": 0}, {"node": "Check High Risk", "type": "main", "index": 0}, {"node": "Log Project Update", "type": "main", "index": 0}]]}, "Check High Risk": {"main": [[{"node": "Alert Management Team", "type": "main", "index": 0}], []]}, "Weekly Report Trigger": {"main": [[{"node": "Read Project Data", "type": "main", "index": 0}]]}, "Read Project Data": {"main": [[{"node": "Generate Weekly Report", "type": "main", "index": 0}]]}, "Generate Weekly Report": {"main": [[{"node": "Send Weekly Report", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "operations", "name": "Operations"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "project-management", "name": "Project Management"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-insights", "name": "AI Insights"}], "triggerCount": 2, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "1-fixed"}