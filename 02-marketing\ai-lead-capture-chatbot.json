{"name": "AI-Powered Lead Capture & Business Q&A Chatbot", "active": false, "nodes": [{"parameters": {"path": "lead-capture", "options": {}}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"model": "gpt-4o", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "You are a helpful business assistant for {{ $json.company_name || 'our company' }}. Your role is to:\n\n1. Answer questions about business hours, services, products, and general company information\n2. Collect lead information when users show interest\n3. Be friendly, professional, and helpful\n\nIf a user shows interest in our services or wants to learn more, collect:\n- Name\n- Email\n- Phone number (optional)\n- Specific interest/need\n\nUser message: {{ $json.message }}\n\nRespond naturally and helpfully. If collecting lead info, ask for one piece of information at a time."}]}, "options": {"temperature": 0.7, "maxTokens": 500}}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "OpenAI Chat", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "operation": "contains"}, "conditions": [{"leftValue": "={{ $('OpenAI Chat').item.json.response }}", "rightValue": "email", "operation": "contains"}], "combineOperation": "any"}}, "id": "check-lead-info", "name": "Check for Lead Info", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"model": "gpt-4o", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Extract lead information from this conversation:\n\nUser message: {{ $json.message }}\nBot response: {{ $('OpenAI Chat').item.json.response }}\n\nExtract and return ONLY a JSON object with these fields (use null if not provided):\n{\n  \"name\": \"extracted name or null\",\n  \"email\": \"extracted email or null\",\n  \"phone\": \"extracted phone or null\",\n  \"interest\": \"extracted interest/need or null\",\n  \"message\": \"original user message\"\n}\n\nReturn only the JSON, no other text."}]}, "options": {"temperature": 0.1, "maxTokens": 200}}, "id": "extract-lead-data", "name": "Extract Lead Data", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"operation": "append", "documentId": "{{ $json.sheet_id || '1BvEQzKj9Kn8YxVqGpF2mH3sL4tR5uI6oP7qW8eR9tY0' }}", "sheetName": "Leads", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ new Date().toISOString() }}", "name": "={{ JSON.parse($('Extract Lead Data').item.json.response).name }}", "email": "={{ JSON.parse($('Extract Lead Data').item.json.response).email }}", "phone": "={{ JSON.parse($('Extract Lead Data').item.json.response).phone }}", "interest": "={{ JSON.parse($('Extract Lead Data').item.json.response).interest }}", "message": "={{ JSON.parse($('Extract Lead Data').item.json.response).message }}", "source": "Website Chatbot"}}, "options": {}}, "id": "save-to-sheets", "name": "Save Lead to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1120, 200]}, {"parameters": {"resource": "message", "operation": "send", "chatId": "{{ $json.notification_chat_id || '@your_team_channel' }}", "text": "🎯 New Lead Captured!\n\n👤 Name: {{ JSON.parse($('Extract Lead Data').item.json.response).name || 'Not provided' }}\n📧 Email: {{ JSON.parse($('Extract Lead Data').item.json.response).email || 'Not provided' }}\n📱 Phone: {{ JSON.parse($('Extract Lead Data').item.json.response).phone || 'Not provided' }}\n💡 Interest: {{ JSON.parse($('Extract Lead Data').item.json.response).interest || 'Not specified' }}\n💬 Message: {{ JSON.parse($('Extract Lead Data').item.json.response).message }}\n\n🕒 Time: {{ new Date().toLocaleString() }}\n📊 Source: Website Chatbot", "additionalFields": {"parse_mode": "HTML"}}, "id": "notify-team", "name": "Notify Team via Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"response\": $('OpenAI Chat').item.json.response,\n  \"timestamp\": new Date().toISOString(),\n  \"lead_captured\": $('Check for Lead Info').item.json ? true : false\n} }}"}, "id": "webhook-response", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"model": "text-embedding-ada-002", "input": "={{ $json.message }}"}, "id": "create-embedding", "name": "Create Message Embedding", "type": "@n8n/n8n-nodes-langchain.openAiEmbeddings", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"operation": "search", "index": "{{ $json.pinecone_index || 'company-knowledge' }}", "namespace": "{{ $json.pinecone_namespace || 'qa' }}", "vector": "={{ $('Create Message Embedding').item.json.embedding }}", "topK": 3, "includeMetadata": true}, "id": "search-knowledge", "name": "Search Company Knowledge", "type": "n8n-nodes-base.pinecone", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"model": "gpt-4o", "messages": {"messageType": "multiModal", "multiModalMessageValue": [{"type": "text", "text": "Based on this company knowledge:\n\n{{ $('Search Company Knowledge').item.json.matches.map(m => m.metadata.text).join('\\n\\n') }}\n\nAnswer this customer question: {{ $json.message }}\n\nBe helpful, accurate, and professional. If you don't have specific information, say so politely and offer to connect them with someone who can help."}]}, "options": {"temperature": 0.3, "maxTokens": 400}}, "id": "knowledge-based-response", "name": "Generate Knowledge-Based Response", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [900, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "OpenAI Chat", "type": "main", "index": 0}, {"node": "Create Message Embedding", "type": "main", "index": 0}]]}, "OpenAI Chat": {"main": [[{"node": "Check for Lead Info", "type": "main", "index": 0}]]}, "Check for Lead Info": {"main": [[{"node": "Extract Lead Data", "type": "main", "index": 0}], [{"node": "Send Response", "type": "main", "index": 0}]]}, "Extract Lead Data": {"main": [[{"node": "Save Lead to Google Sheets", "type": "main", "index": 0}]]}, "Save Lead to Google Sheets": {"main": [[{"node": "Notify Team via Telegram", "type": "main", "index": 0}]]}, "Notify Team via Telegram": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Create Message Embedding": {"main": [[{"node": "Search Company Knowledge", "type": "main", "index": 0}]]}, "Search Company Knowledge": {"main": [[{"node": "Generate Knowledge-Based Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "marketing", "name": "Marketing"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "lead-generation", "name": "Lead Generation"}, {"createdAt": "2025-01-26T10:00:00.000Z", "updatedAt": "2025-01-26T10:00:00.000Z", "id": "ai-chatbot", "name": "AI Chatbot"}], "triggerCount": 1, "updatedAt": "2025-01-26T10:00:00.000Z", "versionId": "c4d5e6f7-g8h9-0123-cdef-456789012345"}